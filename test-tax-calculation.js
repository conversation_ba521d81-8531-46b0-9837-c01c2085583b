// Test script for tax calculation API
// Run this with: node test-tax-calculation.js

const testTaxCalculation = async () => {
  const testData = {
    orderId: "507f1f77bcf86cd799439011", // Sample MongoDB ObjectId
    amount: 10000, // $100.00 in cents
    currency: "usd",
    customerDetails: {
      address: {
        line1: "123 Main Street",
        city: "San Francisco",
        state: "CA",
        postal_code: "94102",
        country: "US"
      }
    }
  };

  try {
    console.log("Testing tax calculation API...");
    console.log("Test data:", JSON.stringify(testData, null, 2));

    const response = await fetch("http://localhost:5000/api/payments/calculate-tax", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_TEST_TOKEN_HERE" // Replace with actual token
      },
      body: JSON.stringify(testData)
    });

    const result = await response.json();
    
    console.log("\nResponse status:", response.status);
    console.log("Response data:", JSON.stringify(result, null, 2));

    if (result.success && result.data?.taxCalculation) {
      const tax = result.data.taxCalculation;
      console.log("\n✅ Tax calculation successful!");
      console.log(`Subtotal: $${(testData.amount / 100).toFixed(2)}`);
      console.log(`Tax: $${(tax.tax_amount_exclusive / 100).toFixed(2)}`);
      console.log(`Total: $${(tax.amount_total / 100).toFixed(2)}`);
      
      if (tax.fallback) {
        console.log("⚠️  Using fallback tax calculation");
      } else {
        console.log("✅ Using Stripe Tax API");
      }
    } else {
      console.log("❌ Tax calculation failed");
    }

  } catch (error) {
    console.error("❌ Error testing tax calculation:", error.message);
  }
};

// Test the fallback endpoint
const testFallbackTax = async () => {
  try {
    console.log("\n\nTesting fallback tax endpoint...");
    
    const response = await fetch("http://localhost:5000/api/payments/test-tax", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Authorization": "Bearer YOUR_TEST_TOKEN_HERE" // Replace with actual token
      }
    });

    const result = await response.json();
    console.log("Fallback test result:", JSON.stringify(result, null, 2));

  } catch (error) {
    console.error("❌ Error testing fallback:", error.message);
  }
};

// Run tests
console.log("🧪 Starting tax calculation tests...");
console.log("Make sure your backend server is running on http://localhost:5000");
console.log("Replace YOUR_TEST_TOKEN_HERE with a valid JWT token\n");

testTaxCalculation();
testFallbackTax();
