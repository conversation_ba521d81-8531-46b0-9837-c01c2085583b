PORT=5000
MONGODB_URI=mongodb://localhost:27017/xosportshub
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRE=30d
NODE_ENV=development

# SSL Configuration
# Set to 'true' to force HTT<PERSON> even in development mode
# By default, HTTP is used for local development (NODE_ENV=development)
# and HTTPS is used for production/staging environments
USE_HTTPS=false

# AWS S3 Configuration
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_BUCKET_NAME=your_bucket_name
AWS_REGION=your_aws_region

# Stripe Configuration
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Tax Configuration
# Set to 'true' in production to use Stripe Tax API
# Set to 'false' to use hardcoded test rates (development/fallback)
ENABLE_STRIPE_TAX=false

# Email Configuration
EMAIL_SERVICE=gmail
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_email_password
EMAIL_FROM=<EMAIL>
FROM_NAME=XO Sports Hub
FROM_EMAIL=<EMAIL>

# Frontend URL for email links
FRONTEND_URL=http://localhost:5173

# Twilio Configuration
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=your_twilio_phone_number

# Firebase Configuration
FIREBASE_PROJECT_ID=your_firebase_project_id
FIREBASE_SERVICE_ACCOUNT_KEY={"type":"service_account","project_id":"your_project_id","private_key_id":"your_private_key_id","private_key":"-----BEGIN PRIVATE KEY-----\nyour_private_key\n-----END PRIVATE KEY-----\n","client_email":"*****************","client_id":"your_client_id","auth_uri":"https://accounts.google.com/o/oauth2/auth","token_uri":"https://oauth2.googleapis.com/token","auth_provider_x509_cert_url":"https://www.googleapis.com/oauth2/v1/certs","client_x509_cert_url":"your_cert_url"}

# Platform Settings
PLATFORM_COMMISSION=10

# OTP Settings
ENABLE_OTP=true