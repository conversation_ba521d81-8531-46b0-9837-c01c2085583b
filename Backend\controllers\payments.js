const ErrorResponse = require("../utils/errorResponse");
const Payment = require("../models/Payment");
const Order = require("../models/Order");
const User = require("../models/User");
const Card = require("../models/Card");
const stripe = require("stripe")(process.env.STRIPE_SECRET_KEY);
const { validationResult } = require("express-validator");

// Helper function to extract and save card details from payment intent
const extractAndSaveCardDetails = async (paymentIntent, userId) => {
  try {
    // Get the payment method from Stripe
    const paymentMethod = await stripe.paymentMethods.retrieve(
      paymentIntent.payment_method
    );

    if (!paymentMethod || !paymentMethod.card) {
      return null;
    }

    const cardDetails = {
      cardType: paymentMethod.card.brand,
      lastFourDigits: paymentMethod.card.last4,
    };

    // Check if this card already exists for the user
    const existingCard = await Card.findOne({
      user: userId,
      fingerprint: paymentMethod.card.fingerprint,
      isActive: true,
    });

    if (existingCard) {
      // Card already exists, return reference to it
      cardDetails.cardId = existingCard._id;
      return cardDetails;
    }

    // Check if user wants to save new cards automatically (you can add this as user preference)
    // For now, we'll save all new cards used in payments
    try {
      // Get or create Stripe customer for user
      const user = await User.findById(userId);
      let stripeCustomerId = user.paymentInfo?.stripeCustomerId;

      if (!stripeCustomerId) {
        const customer = await stripe.customers.create({
          email: user.email,
          name: `${user.firstName} ${user.lastName}`,
          metadata: {
            userId: user.id,
          },
        });

        stripeCustomerId = customer.id;

        // Update user with Stripe customer ID
        await User.findByIdAndUpdate(userId, {
          "paymentInfo.stripeCustomerId": stripeCustomerId,
        });
      }

      // Attach payment method to customer if not already attached
      if (!paymentMethod.customer) {
        await stripe.paymentMethods.attach(paymentMethod.id, {
          customer: stripeCustomerId,
        });
      }

      // Check if this is the user's first card to make it default
      const userCardCount = await Card.countDocuments({
        user: userId,
        isActive: true,
      });
      const isDefault = userCardCount === 0;

      // Create card record
      const newCard = await Card.create({
        user: userId,
        stripePaymentMethodId: paymentMethod.id,
        lastFourDigits: paymentMethod.card.last4,
        cardType: paymentMethod.card.brand,
        expiryMonth: paymentMethod.card.exp_month,
        expiryYear: paymentMethod.card.exp_year,
        cardholderName:
          paymentMethod.billing_details.name ||
          `${user.firstName} ${user.lastName}`,
        fingerprint: paymentMethod.card.fingerprint,
        isDefault,
        billingAddress: {
          line1: paymentMethod.billing_details.address?.line1,
          line2: paymentMethod.billing_details.address?.line2,
          city: paymentMethod.billing_details.address?.city,
          state: paymentMethod.billing_details.address?.state,
          postalCode: paymentMethod.billing_details.address?.postal_code,
          country: paymentMethod.billing_details.address?.country,
        },
      });

      cardDetails.cardId = newCard._id;
      return cardDetails;
    } catch (cardSaveError) {
      console.error("Error saving card details:", cardSaveError);
      // Return basic card details even if saving fails
      return cardDetails;
    }
  } catch (error) {
    console.error("Error extracting card details:", error);
    return null;
  }
};

// @desc    Get all payments
// @route   GET /api/payments
// @access  Private/Admin
exports.getPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find()
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get single payment
// @route   GET /api/payments/:id
// @access  Private
exports.getPayment = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id)
      .populate({
        path: "buyer",
        select: "firstName lastName email",
      })
      .populate({
        path: "seller",
        select: "firstName lastName email",
      })
      .populate("order");

    if (!payment) {
      return next(
        new ErrorResponse(`Payment not found with id of ${req.params.id}`, 404)
      );
    }

    // Make sure user is payment buyer or seller or admin
    if (
      payment.buyer._id.toString() !== req.user.id &&
      payment.seller._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to view this payment`,
          403
        )
      );
    }

    res.status(200).json({
      success: true,
      data: payment,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Create payment intent
// @route   POST /api/payments/create-intent
// @access  Private/Buyer
exports.createPaymentIntent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { orderId } = req.body;

    // Get order with seller information
    const order = await Order.findById(orderId)
      .populate("content")
      .populate("seller", "paymentInfo firstName lastName");

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${orderId}`, 404)
      );
    }

    // Make sure user is order buyer
    if (order.buyer.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to pay for this order`,
          403
        )
      );
    }

    // Check if order is already paid
    if (order.paymentStatus === "Completed") {
      return next(new ErrorResponse(`Order is already paid`, 400));
    }

    // Check if payment deadline has expired
    if (order.paymentDeadline && new Date() > order.paymentDeadline) {
      return next(
        new ErrorResponse(
          `Payment deadline has expired. This order can no longer be paid.`,
          400
        )
      );
    }

    // Check if seller has completed Stripe Connect onboarding
    const seller = order.seller;
    if (!seller.paymentInfo?.stripeConnectId) {
      return next(
        new ErrorResponse(
          `Seller has not completed payment setup. Please contact seller to complete their payment onboarding.`,
          400
        )
      );
    }

    // Verify seller's Stripe Connect account is active
    try {
      const connectAccount = await stripe.accounts.retrieve(
        seller.paymentInfo.stripeConnectId
      );
      if (
        !connectAccount.details_submitted ||
        !connectAccount.charges_enabled
      ) {
        return next(
          new ErrorResponse(
            `Seller's payment account is not fully set up. Please contact seller.`,
            400
          )
        );
      }
    } catch (stripeError) {
      return next(
        new ErrorResponse(
          `Seller's payment account is invalid. Please contact seller.`,
          400
        )
      );
    }

    // Get or create Stripe customer for user
    let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        metadata: {
          userId: req.user.id,
        },
      });

      stripeCustomerId = customer.id;

      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(req.user.id, {
        "paymentInfo.stripeCustomerId": stripeCustomerId,
      });
    }

    // Calculate platform fee and seller amount using environment variable
    const totalAmount = order.amount;
    const platformFeePercentage = process.env.PLATFORM_COMMISSION || 10;
    const platformFeeDecimal = platformFeePercentage / 100;
    const sellerEarningsDecimal = 1 - platformFeeDecimal;
    const platformFeeAmount = Math.round(
      totalAmount * platformFeeDecimal * 100
    ); // Platform fee in cents
    const sellerAmount = Math.round(totalAmount * sellerEarningsDecimal * 100); // Seller amount in cents

    // Create payment intent with Stripe Connect transfer
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Total amount in cents
      currency: "usd",
      customer: stripeCustomerId,
      description: `Payment for ${order.content.title || "Digital Content"
        } - Order #${order._id}`,
      metadata: {
        orderId: order._id.toString(),
        contentId: order.content._id.toString(),
        buyerId: req.user.id,
        sellerId: order.seller._id.toString(),
        orderType: order.orderType,
        platformFee: (platformFeeAmount / 100).toString(),
        sellerEarnings: (sellerAmount / 100).toString(),
      },
      // Stripe Connect configuration for automatic transfers
      transfer_data: {
        destination: seller.paymentInfo.stripeConnectId,
      },
      application_fee_amount: platformFeeAmount, // Platform commission from environment variable
      // Required for Indian regulations
      shipping: {
        name: req.user.name || "Digital Content Buyer",
        address: {
          line1: "Digital Content Delivery",
          city: "Online",
          state: "Digital",
          postal_code: "000000",
          country: "IN",
        },
      },
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Confirm payment
// @route   POST /api/payments/confirm
// @access  Private/Buyer
exports.confirmPayment = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { paymentIntentId, orderId } = req.body;

    // Get order
    const order = await Order.findById(orderId)
      .populate("buyer", "firstName lastName email mobile")
      .populate("content", "title sport contentType");

    if (!order) {
      return next(
        new ErrorResponse(`Order not found with id of ${orderId}`, 404)
      );
    }

    // Make sure user is order buyer
    if (
      order.buyer._id.toString() !== req.user.id &&
      req.user.role !== "admin"
    ) {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to confirm payment for this order`,
          403
        )
      );
    }

    // Get payment intent from Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== "succeeded") {
      return next(new ErrorResponse(`Payment has not been completed`, 400));
    }

    // Extract and save card details
    const cardDetails = await extractAndSaveCardDetails(
      paymentIntent,
      req.user.id
    );

    // Update order
    order.paymentStatus = "Completed";
    order.paymentIntentId = paymentIntentId;
    order.status = "Completed";

    // Add card details to order if available
    if (cardDetails) {
      order.cardDetails = {
        cardType: cardDetails.cardType,
        lastFourDigits: cardDetails.lastFourDigits,
      };
    }

    await order.save();

    // Create payment record with card details
    const paymentData = {
      order: orderId,
      buyer: order.buyer._id,
      seller: order.seller,
      amount: order.amount,
      platformFee: order.platformFee,
      sellerEarnings: order.sellerEarnings,
      totalAmount: order.totalAmount || order.amount, // Buyer pays exactly the listed amount
      paymentMethod: "card",
      paymentIntentId,
      status: "Completed",
      payoutStatus: "Pending",
    };

    // Add card details if available
    if (cardDetails) {
      paymentData.cardDetails = cardDetails;
    }

    const payment = await Payment.create(paymentData);

    // Mark content as sold and update auction status if applicable
    if (order.content) {
      const Content = require("../models/Content");
      const content = await Content.findById(order.content);

      if (content && content.saleType === "Auction") {
        await Content.findByIdAndUpdate(order.content, {
          isSold: true,
          soldAt: new Date(),
          auctionStatus: "Ended",
          auctionEndedAt: new Date(),
        });
      }
    }

    // Send order receipt email
    try {
      const { orderReceiptTemplate } = require("../utils/emailTemplates");
      const sendEmail = require("../utils/sendEmail");

      const emailData = orderReceiptTemplate({
        order,
        buyer: order.buyer,
        content: order.content,
        cardDetails: order.cardDetails,
      });

      await sendEmail({
        email: order.buyer.email,
        subject: emailData.subject,
        message: emailData.message,
        html: emailData.html,
      });

      console.log(`Order receipt email sent to ${order.buyer.email}`);
    } catch (emailError) {
      console.error("Error sending order receipt email:", emailError);
      // Don't fail the payment confirmation if email fails
    }

    res.status(200).json({
      success: true,
      data: payment,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Process Stripe webhook
// @route   POST /api/payments/webhook
// @access  Public
exports.webhook = async (req, res, next) => {
  try {
    let event;

    if (process.env.NODE_ENV === "development") {
      // 🔓 Bypass signature verification in development
      event = req.body;
    } else {
      // 🔐 Verify signature in production
      const sig = req.headers["stripe-signature"];

      try {
        event = stripe.webhooks.constructEvent(
          req.body,
          sig,
          process.env.STRIPE_WEBHOOK_SECRET
        );
      } catch (err) {
        return res.status(400).send(`Webhook Error: ${err.message}`);
      }
    }

    // Handle the event
    if (event.type === "payment_intent.succeeded") {
      const paymentIntent = event.data.object;
      const orderId = paymentIntent.metadata?.orderId;

      if (orderId) {
        const order = await Order.findById(orderId)
          .populate("buyer", "firstName lastName email mobile")
          .populate("content", "title sport contentType");

        if (order) {
          // Extract and save card details
          const cardDetails = await extractAndSaveCardDetails(
            paymentIntent,
            order.buyer._id
          );

          order.paymentStatus = "Completed";
          order.paymentIntentId = paymentIntent.id;
          order.status = "Completed";

          // Add card details to order if available
          if (cardDetails) {
            order.cardDetails = {
              cardType: cardDetails.cardType,
              lastFourDigits: cardDetails.lastFourDigits,
            };
          }

          await order.save();

          // Mark content as sold and update auction status if applicable
          if (order.content) {
            const Content = require("../models/Content");
            const content = await Content.findById(order.content);

            if (content && content.saleType === "Auction") {
              await Content.findByIdAndUpdate(order.content, {
                isSold: true,
                soldAt: new Date(),
                auctionStatus: "Ended",
                auctionEndedAt: new Date(),
              });
            }
          }

          const existingPayment = await Payment.findOne({
            paymentIntentId: paymentIntent.id,
          });

          if (!existingPayment) {
            // Create payment record with card details (reuse the same cardDetails)
            const paymentData = {
              order: orderId,
              buyer: order.buyer._id,
              seller: order.seller,
              amount: order.amount,
              platformFee: order.platformFee,
              sellerEarnings: order.sellerEarnings,
              totalAmount: order.totalAmount || order.amount, // Buyer pays exactly the listed amount
              paymentMethod: "card",
              paymentIntentId: paymentIntent.id,
              status: "Completed",
              payoutStatus: "Pending",
            };

            // Add card details if available
            if (cardDetails) {
              paymentData.cardDetails = cardDetails;
            }

            await Payment.create(paymentData);

            // Send order receipt email
            try {
              const {
                orderReceiptTemplate,
              } = require("../utils/emailTemplates");
              const sendEmail = require("../utils/sendEmail");

              const emailData = orderReceiptTemplate({
                order,
                buyer: order.buyer,
                content: order.content,
                cardDetails: order.cardDetails,
              });

              await sendEmail({
                email: order.buyer.email,
                subject: emailData.subject,
                message: emailData.message,
                html: emailData.html,
              });

              console.log(
                `Order receipt email sent to ${order.buyer.email} via webhook`
              );
            } catch (emailError) {
              console.error(
                "Error sending order receipt email from webhook:",
                emailError
              );
              // Don't fail the webhook handling if email fails
            }
          }
        }
      }
    }

    res.status(200).json({ received: true });
  } catch (err) {
    next(err);
  }
};

// @desc    Get buyer payments
// @route   GET /api/payments/buyer
// @access  Private/Buyer
exports.getBuyerPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find({ buyer: req.user.id })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Get seller payments
// @route   GET /api/payments/seller
// @access  Private/Seller
exports.getSellerPayments = async (req, res, next) => {
  try {
    const payments = await Payment.find({ seller: req.user.id })
      .populate("order")
      .sort("-createdAt");

    res.status(200).json({
      success: true,
      count: payments.length,
      data: payments,
    });
  } catch (err) {
    next(err);
  }
};

// @desc    Process payout
// @route   POST /api/payments/:id/payout
// @access  Private/Admin
exports.processPayout = async (req, res, next) => {
  try {
    const payment = await Payment.findById(req.params.id).populate(
      "seller",
      "paymentInfo firstName lastName email"
    );

    if (!payment) {
      return next(
        new ErrorResponse(`Payment not found with id of ${req.params.id}`, 404)
      );
    }

    if (payment.payoutStatus !== "Pending") {
      return next(
        new ErrorResponse(`Payment payout has already been processed`, 400)
      );
    }

    const seller = payment.seller;

    // Check if seller has Stripe Connect ID
    if (!seller.paymentInfo || !seller.paymentInfo.stripeConnectId) {
      return next(
        new ErrorResponse(`Seller does not have a Stripe Connect account`, 400)
      );
    }

    try {
      // Process payout through Stripe
      const payout = await stripe.transfers.create({
        amount: Math.round(payment.sellerEarnings * 100), // Convert to cents
        currency: "usd",
        destination: seller.paymentInfo.stripeConnectId,
        description: `Payout for order ${payment.order}`,
        metadata: {
          paymentId: payment._id.toString(),
          orderId: payment.order.toString(),
          sellerId: seller._id.toString(),
        },
      });

      // Update payment
      payment.payoutStatus = "Completed";
      payment.payoutId = payout.id;
      payment.payoutDate = new Date();
      await payment.save();

      res.status(200).json({
        success: true,
        data: payment,
        payout,
      });
    } catch (stripeError) {
      console.error("Stripe payout error:", stripeError);

      // Update payment status to failed
      payment.payoutStatus = "Failed";
      await payment.save();

      return next(
        new ErrorResponse(`Payout failed: ${stripeError.message}`, 400)
      );
    }
  } catch (err) {
    next(err);
  }
};

// @desc    Create Stripe Connect account
// @route   POST /api/payments/create-connect-account
// @access  Private/Seller
exports.createConnectAccount = async (req, res, next) => {
  try {
    const { email, firstName, lastName } = req.body;
    const userId = req.user.id;

    // Check if user already has a Stripe Connect account
    const user = await User.findById(userId);
    let accountId = user.paymentInfo?.stripeConnectId;

    if (accountId) {
      // User already has an account, create account link for completion
      try {
        // Verify the account still exists in Stripe
        const existingAccount = await stripe.accounts.retrieve(accountId);

        // Check if this is for onboarding or settings
        const isOnboarding = req.body.context === "onboarding";
        const baseUrl = isOnboarding
          ? "/stripe-onboarding-return"
          : "/seller/payment-settings";

        // Create account link for existing account to complete onboarding
        const accountLink = await stripe.accountLinks.create({
          account: accountId,
          refresh_url: `${process.env.FRONTEND_URL
            }${baseUrl}?stripe_refresh=true&account_id=${accountId}${isOnboarding ? "&context=onboarding" : ""
            }`,
          return_url: `${process.env.FRONTEND_URL
            }${baseUrl}?stripe_success=true&account_id=${accountId}${isOnboarding ? "&context=onboarding" : ""
            }`,
          type: "account_onboarding",
          collect: "eventually_due",
        });

        return res.status(200).json({
          success: true,
          data: {
            accountId: accountId,
            onboardingUrl: accountLink.url,
            accountType: existingAccount.type || "express",
            isExisting: true,
          },
        });
      } catch (stripeError) {
        // If account doesn't exist in Stripe anymore, clear it and create new one
        if (stripeError.code === "resource_missing") {
          await User.findByIdAndUpdate(userId, {
            $unset: { "paymentInfo.stripeConnectId": 1 },
          });
          accountId = null;
        } else {
          throw stripeError;
        }
      }
    }

    // Create new Stripe Express account
    const account = await stripe.accounts.create({
      type: "express",
      country: "US",
      email: email,
      capabilities: {
        card_payments: { requested: true },
        transfers: { requested: true },
      },
      business_type: "individual",
      individual: {
        first_name: firstName,
        last_name: lastName,
        email: email,
      },
      metadata: {
        user_id: userId,
        platform: "xosportshub",
      },
    });

    // Check if this is for onboarding or settings
    const isOnboarding = req.body.context === "onboarding";
    const baseUrl = isOnboarding
      ? "/stripe-onboarding-return"
      : "/seller/payment-settings";

    // Create account link for Express account onboarding
    const accountLink = await stripe.accountLinks.create({
      account: account.id,
      refresh_url: `${process.env.FRONTEND_URL
        }${baseUrl}?stripe_refresh=true&account_id=${account.id}${isOnboarding ? "&context=onboarding" : ""
        }`,
      return_url: `${process.env.FRONTEND_URL
        }${baseUrl}?stripe_success=true&account_id=${account.id}${isOnboarding ? "&context=onboarding" : ""
        }`,
      type: "account_onboarding",
      collect: "eventually_due", // Collect all required information
    });

    // Update user with Stripe Connect account ID (don't mark as complete yet)
    await User.findByIdAndUpdate(userId, {
      "paymentInfo.stripeConnectId": account.id,
    });

    res.status(200).json({
      success: true,
      data: {
        accountId: account.id,
        onboardingUrl: accountLink.url,
        accountType: "express",
        isExisting: false,
      },
    });
  } catch (err) {
    console.error("Stripe Connect account creation error:", err);
    next(new ErrorResponse("Failed to create Stripe Connect account", 500));
  }
};

// @desc    Get Stripe Connect account status
// @route   GET /api/payments/connect-account-status/:accountId
// @access  Private/Seller
exports.getConnectAccountStatus = async (req, res, next) => {
  try {
    const { accountId } = req.params;
    const userId = req.user.id;

    // Verify the account belongs to the user
    const user = await User.findById(userId);
    if (user.paymentInfo?.stripeConnectId !== accountId) {
      return next(
        new ErrorResponse("Unauthorized access to this account", 403)
      );
    }

    // Get account details from Stripe
    const account = await stripe.accounts.retrieve(accountId);

    res.status(200).json({
      success: true,
      data: {
        accountId: account.id,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        requirements: account.requirements,
      },
    });
  } catch (err) {
    console.error("Stripe Connect account status error:", err);
    next(new ErrorResponse("Failed to get account status", 500));
  }
};

// @desc    Calculate tax using Stripe Tax API
// @route   POST /api/payments/calculate-tax
// @access  Private (Buyer)
exports.calculateTax = async (req, res, next) => {
  try {
    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log("Validation errors:", errors.array());
      return next(new ErrorResponse("Validation failed", 400));
    }

    const { orderId, amount, currency = 'usd', customerDetails } = req.body;

    console.log("Tax calculation request:", {
      orderId,
      amount,
      currency,
      customerDetails
    });

    // Validate required fields
    if (!orderId || !amount || !customerDetails?.address) {
      console.log("Validation failed - missing required fields");
      return next(new ErrorResponse("Order ID, amount, and customer address are required", 400));
    }

    // Verify order exists and belongs to user
    const order = await Order.findById(orderId);
    if (!order) {
      return next(new ErrorResponse("Order not found", 404));
    }

    // Check if user owns this order (buyers can only calculate tax for their own orders)
    if (order.buyer.toString() !== req.user.id && req.user.role !== 'admin') {
      return next(new ErrorResponse("Not authorized to calculate tax for this order", 403));
    }

    // Prepare line items for Stripe Tax calculation
    const lineItems = [{
      amount: Math.round(amount), // Amount should be in cents
      reference: `order_${orderId}`,
      tax_behavior: 'exclusive', // Tax is calculated on top of the amount
      tax_code: 'txcd_10000000', // Digital goods tax code
    }];

    // Log the request to Stripe
    console.log("Stripe tax calculation request:", {
      currency: currency.toLowerCase(),
      line_items: lineItems,
      customer_details: {
        address: {
          line1: customerDetails.address.line1,
          city: customerDetails.address.city,
          state: customerDetails.address.state,
          postal_code: customerDetails.address.postal_code,
          country: customerDetails.address.country || 'US',
        }
      }
    });

    // Create tax calculation with Stripe
    const taxCalculation = await stripe.tax.calculations.create({
      currency: currency.toLowerCase(),
      line_items: lineItems,
      customer_details: {
        address: {
          line1: customerDetails.address.line1,
          city: customerDetails.address.city,
          state: customerDetails.address.state,
          postal_code: customerDetails.address.postal_code,
          country: customerDetails.address.country || 'US',
        },
        address_source: 'billing',
      },
      expand: ['line_items.data.tax_breakdown'],
    });

    console.log("Stripe tax calculation response:", {
      id: taxCalculation.id,
      amount_total: taxCalculation.amount_total,
      tax_amount_exclusive: taxCalculation.tax_amount_exclusive,
      tax_amount_inclusive: taxCalculation.tax_amount_inclusive,
    });

    // Format the response
    const formattedResponse = {
      success: true,
      data: {
        taxCalculation: {
          id: taxCalculation.id,
          amount_total: taxCalculation.amount_total,
          tax_amount_exclusive: taxCalculation.tax_amount_exclusive,
          tax_amount_inclusive: taxCalculation.tax_amount_inclusive,
          line_items: taxCalculation.line_items.data.map(item => ({
            amount: item.amount,
            amount_tax: item.amount_tax,
            tax_breakdown: item.tax_breakdown,
          })),
          customer_details: taxCalculation.customer_details,
        },
      },
    };

    console.log("Sending tax calculation response:", formattedResponse);
    res.status(200).json(formattedResponse);

  } catch (err) {
    console.error("Tax calculation error:", err);

    // Handle specific Stripe errors
    if (err.type === 'StripeInvalidRequestError') {
      console.log("Stripe tax calculation not available, using fallback");

      // Fallback: Basic tax calculation based on state
      const stateTaxRates = {
        'CA': 0.0725, // California
        'NY': 0.08,   // New York
        'TX': 0.0625, // Texas
        'FL': 0.06,   // Florida
        'WA': 0.065,  // Washington
        'OR': 0.0,    // Oregon (no sales tax)
        'NH': 0.0,    // New Hampshire (no sales tax)
        'MT': 0.0,    // Montana (no sales tax)
        'DE': 0.0,    // Delaware (no sales tax)
        'AK': 0.0,    // Alaska (no state sales tax)
      };

      const state = customerDetails.address.state;
      const taxRate = stateTaxRates[state] || 0.05; // Default 5% if state not found
      const taxAmount = Math.round(amount * taxRate);

      console.log(`Using fallback tax calculation for ${state}: ${taxRate * 100}%`);

      return res.status(200).json({
        success: true,
        data: {
          taxCalculation: {
            id: `fallback_${Date.now()}`,
            amount_total: amount + taxAmount,
            tax_amount_exclusive: taxAmount,
            tax_amount_inclusive: 0,
            line_items: [{
              amount: amount,
              amount_tax: taxAmount,
              tax_breakdown: [{
                jurisdiction: { display_name: state },
                tax_rate: taxRate,
                taxable_amount: amount,
                tax_amount: taxAmount
              }]
            }],
            customer_details: customerDetails,
            fallback: true
          },
        },
      });
    }

    next(new ErrorResponse("Failed to calculate tax", 500));
  }
};

// @desc    Create Stripe dashboard link for Connect account
// @route   POST /api/payments/create-dashboard-link
// @access  Private/Seller
exports.createDashboardLink = async (req, res, next) => {
  try {
    const userId = req.user.id;

    // Get user's Stripe Connect account
    const user = await User.findById(userId);
    if (!user.paymentInfo?.stripeConnectId) {
      return next(new ErrorResponse("No Stripe Connect account found", 400));
    }

    const accountId = user.paymentInfo.stripeConnectId;

    // First, retrieve the account to check its type and status
    const account = await stripe.accounts.retrieve(accountId);

    // Check if account is properly set up
    if (!account.details_submitted) {
      return next(
        new ErrorResponse(
          "Account setup is not complete. Please complete your account setup first.",
          400
        )
      );
    }

    // For Express accounts, create a login link
    if (account.type === "express") {
      try {
        const loginLink = await stripe.accounts.createLoginLink(accountId);

        return res.status(200).json({
          success: true,
          data: {
            dashboardUrl: loginLink.url,
            accountType: "express",
          },
        });
      } catch (loginError) {
        // If login link creation fails, provide alternative access
        console.error("Login link creation failed:", loginError);

        // Return a direct link to Stripe Dashboard
        return res.status(200).json({
          success: true,
          data: {
            dashboardUrl: `https://dashboard.stripe.com/dashboard`,
            accountType: "express",
            message:
              "Please log in to your Stripe account to access the dashboard",
          },
        });
      }
    }

    // For Standard accounts (legacy support), create a login link
    if (account.type === "standard") {
      try {
        const loginLink = await stripe.accounts.createLoginLink(accountId);

        return res.status(200).json({
          success: true,
          data: {
            dashboardUrl: loginLink.url,
            accountType: "standard",
          },
        });
      } catch (loginError) {
        // If login link creation fails, provide alternative access
        console.error("Login link creation failed:", loginError);

        // Return a direct link to Stripe Dashboard
        return res.status(200).json({
          success: true,
          data: {
            dashboardUrl: `https://dashboard.stripe.com/dashboard`,
            accountType: "standard",
            message:
              "Please log in to your Stripe account to access the dashboard",
          },
        });
      }
    }

    // For custom accounts or other types
    return res.status(200).json({
      success: true,
      data: {
        dashboardUrl: `https://dashboard.stripe.com/dashboard`,
        accountType: account.type || "unknown",
        message: "Please log in to your Stripe account to access the dashboard",
      },
    });
  } catch (err) {
    console.error("Stripe dashboard link creation error:", err);

    // Provide a fallback response instead of just throwing an error
    if (err.type === "StripeInvalidRequestError") {
      return res.status(200).json({
        success: true,
        data: {
          dashboardUrl: `https://dashboard.stripe.com/dashboard`,
          accountType: "fallback",
          message:
            "Please log in to your Stripe account to access the dashboard",
        },
      });
    }

    next(new ErrorResponse("Failed to create dashboard link", 500));
  }
};

// @desc    Update Connect account with additional information
// @route   POST /api/payments/update-connect-account
// @access  Private/Seller
exports.updateConnectAccount = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const { accountId, businessType, businessProfile } = req.body;

    // Verify the account belongs to the user
    const user = await User.findById(userId);
    if (user.paymentInfo?.stripeConnectId !== accountId) {
      return next(
        new ErrorResponse("Unauthorized access to this account", 403)
      );
    }

    // Update account with additional information for Express accounts
    const updateData = {};

    if (businessType) {
      updateData.business_type = businessType;
    }

    if (businessProfile) {
      updateData.business_profile = businessProfile;
    }

    // Update the account in Stripe
    const account = await stripe.accounts.update(accountId, updateData);

    res.status(200).json({
      success: true,
      data: {
        accountId: account.id,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        requirements: account.requirements,
        capabilities: account.capabilities,
      },
    });
  } catch (err) {
    console.error("Stripe Connect account update error:", err);
    next(new ErrorResponse("Failed to update Stripe Connect account", 500));
  }
};

// @desc    Get detailed account information including capabilities
// @route   GET /api/payments/connect-account-details/:accountId
// @access  Private/Seller
exports.getConnectAccountDetails = async (req, res, next) => {
  try {
    const { accountId } = req.params;
    const userId = req.user.id;

    // Verify the account belongs to the user
    const user = await User.findById(userId);
    if (user.paymentInfo?.stripeConnectId !== accountId) {
      return next(
        new ErrorResponse("Unauthorized access to this account", 403)
      );
    }

    // Get detailed account information from Stripe
    const account = await stripe.accounts.retrieve(accountId);

    res.status(200).json({
      success: true,
      data: {
        accountId: account.id,
        accountType: account.type,
        email: account.email,
        detailsSubmitted: account.details_submitted,
        chargesEnabled: account.charges_enabled,
        payoutsEnabled: account.payouts_enabled,
        requirements: account.requirements,
        capabilities: account.capabilities,
        businessType: account.business_type,
        country: account.country,
        defaultCurrency: account.default_currency,
        individual: account.individual,
        businessProfile: account.business_profile,
        futureRequirements: account.future_requirements,
        currentlyDue: account.requirements?.currently_due || [],
        eventuallyDue: account.requirements?.eventually_due || [],
        pastDue: account.requirements?.past_due || [],
      },
    });
  } catch (err) {
    console.error("Stripe Connect account details error:", err);
    next(new ErrorResponse("Failed to get account details", 500));
  }
};

// @desc    Create payment intent for bid purchase
// @route   POST /api/payments/create-bid-payment-intent
// @access  Private/Buyer
exports.createBidPaymentIntent = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { bidId, amount } = req.body;
    const Bid = require("../models/Bid");

    // Get bid with content and seller information
    const bid = await Bid.findById(bidId).populate({
      path: "content",
      populate: {
        path: "seller",
        select: "paymentInfo firstName lastName",
      },
    });

    if (!bid) {
      return next(new ErrorResponse(`Bid not found with id of ${bidId}`, 404));
    }

    // Make sure user is the bidder
    if (bid.bidder.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to pay for this bid`,
          403
        )
      );
    }

    // Check if bid is in "Won" status
    if (bid.status !== "Won") {
      return next(
        new ErrorResponse(
          `Bid must be in 'Won' status to proceed with payment`,
          400
        )
      );
    }

    // Check if seller has completed Stripe Connect onboarding
    const seller = bid.content.seller;
    if (!seller.paymentInfo?.stripeConnectId) {
      return next(
        new ErrorResponse(
          `Seller has not completed payment setup. Please contact seller to complete their payment onboarding.`,
          400
        )
      );
    }

    // Verify seller's Stripe Connect account is active
    try {
      const connectAccount = await stripe.accounts.retrieve(
        seller.paymentInfo.stripeConnectId
      );
      if (
        !connectAccount.details_submitted ||
        !connectAccount.charges_enabled
      ) {
        return next(
          new ErrorResponse(
            `Seller's payment account is not fully set up. Please contact seller.`,
            400
          )
        );
      }
    } catch (stripeError) {
      return next(
        new ErrorResponse(
          `Seller's payment account is invalid. Please contact seller.`,
          400
        )
      );
    }

    // Calculate fees
    const bidAmount = parseFloat(amount);
    const platformFeePercentage =
      parseFloat(process.env.PLATFORM_COMMISSION) || 10;
    const platformFeeAmount = Math.round(
      ((bidAmount * platformFeePercentage) / 100) * 100
    ); // in cents
    const totalAmount = bidAmount; // Buyer pays exactly the bid amount, not bid amount + platform fee

    // Get or create Stripe customer
    let stripeCustomerId = req.user.paymentInfo?.stripeCustomerId;

    if (!stripeCustomerId) {
      const customer = await stripe.customers.create({
        email: req.user.email,
        name: `${req.user.firstName} ${req.user.lastName}`,
        metadata: {
          userId: req.user.id,
        },
      });

      stripeCustomerId = customer.id;

      // Update user with Stripe customer ID
      await User.findByIdAndUpdate(req.user.id, {
        "paymentInfo.stripeCustomerId": stripeCustomerId,
      });
    }

    // Create payment intent with Stripe Connect transfer
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // Total amount in cents
      currency: "usd",
      customer: stripeCustomerId,
      description: `Payment for bid on ${bid.content.title || "Digital Content"
        } - Bid #${bid._id}`,
      metadata: {
        bidId: bid._id.toString(),
        contentId: bid.content._id.toString(),
        buyerId: req.user.id,
        sellerId: bid.content.seller._id.toString(),
        orderType: "Auction",
        bidAmount: bidAmount.toString(),
        platformFee: (platformFeeAmount / 100).toString(),
        totalAmount: totalAmount.toString(),
      },
      // Stripe Connect configuration for automatic transfers
      transfer_data: {
        destination: seller.paymentInfo.stripeConnectId,
      },
      application_fee_amount: platformFeeAmount, // Platform commission
      // Required for some regulations
      shipping: {
        name: req.user.name || "Digital Content Buyer",
        address: {
          line1: "Digital Content Delivery",
          city: "Online",
          state: "Digital",
          postal_code: "000000",
          country: "US",
        },
      },
    });

    res.status(200).json({
      success: true,
      clientSecret: paymentIntent.client_secret,
    });
  } catch (err) {
    console.error("Error creating bid payment intent:", err);
    next(err);
  }
};

// @desc    Complete bid purchase after successful payment
// @route   POST /api/payments/complete-bid-purchase
// @access  Private/Buyer
exports.completeBidPurchase = async (req, res, next) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, errors: errors.array() });
    }

    const { bidId, paymentIntentId } = req.body;
    const Bid = require("../models/Bid");
    const Order = require("../models/Order");

    // Get bid with content information
    const bid = await Bid.findById(bidId)
      .populate("content")
      .populate("content.seller", "firstName lastName");

    if (!bid) {
      return next(new ErrorResponse(`Bid not found with id of ${bidId}`, 404));
    }

    // Make sure user is the bidder
    if (bid.bidder.toString() !== req.user.id && req.user.role !== "admin") {
      return next(
        new ErrorResponse(
          `User ${req.user.id} is not authorized to complete this purchase`,
          403
        )
      );
    }

    // Verify payment intent with Stripe
    const paymentIntent = await stripe.paymentIntents.retrieve(paymentIntentId);

    if (paymentIntent.status !== "succeeded") {
      return next(new ErrorResponse("Payment was not successful", 400));
    }

    // Check if order already exists for this bid
    const existingOrder = await Order.findOne({ bidId: bid._id });
    if (existingOrder && existingOrder.paymentStatus === "Completed") {
      return next(
        new ErrorResponse("Purchase has already been completed", 400)
      );
    }

    // Extract card details from payment intent
    const cardDetails = await extractAndSaveCardDetails(
      paymentIntent,
      req.user.id
    );

    // Calculate amounts
    const bidAmount = bid.amount;
    const platformFeePercentage =
      parseFloat(process.env.PLATFORM_COMMISSION) || 10;
    const platformFee = bidAmount * (platformFeePercentage / 100);
    const sellerEarnings =
      bidAmount - bidAmount * (platformFeePercentage / 100);
    const totalAmount = bidAmount; // Buyer pays exactly the bid amount, not bid amount + platform fee

    // Create or update order
    let order;
    if (existingOrder) {
      // Update existing order
      order = await Order.findByIdAndUpdate(
        existingOrder._id,
        {
          paymentStatus: "Completed",
          status: "Completed",
          paymentIntentId,
          paymentMethod: "card",
          cardDetails: cardDetails
            ? {
              cardType: cardDetails.cardType,
              lastFourDigits: cardDetails.lastFourDigits,
            }
            : undefined,
        },
        { new: true }
      );
    } else {
      // Create new order
      order = await Order.create({
        buyer: bid.bidder,
        seller: bid.content.seller,
        content: bid.content._id,
        orderType: "Auction",
        amount: bidAmount,
        platformFee,
        sellerEarnings,
        totalAmount,
        bidId: bid._id,
        paymentStatus: "Completed",
        status: "Completed",
        paymentIntentId,
        paymentMethod: "card",
        cardDetails: cardDetails
          ? {
            cardType: cardDetails.cardType,
            lastFourDigits: cardDetails.lastFourDigits,
          }
          : undefined,
      });
    }

    // Create payment record
    const paymentData = {
      order: order._id,
      buyer: bid.bidder,
      seller: bid.content.seller,
      amount: bidAmount,
      platformFee,
      sellerEarnings,
      totalAmount,
      paymentMethod: "card",
      paymentIntentId,
      status: "Completed",
      payoutStatus: "Pending",
    };

    // Add card details if available
    if (cardDetails) {
      paymentData.cardDetails = cardDetails;
    }

    const payment = await Payment.create(paymentData);

    // Mark content as sold and update auction status if applicable
    const Content = require("../models/Content");
    const content = await Content.findById(bid.content._id);

    if (content && content.saleType === "Auction") {
      await Content.findByIdAndUpdate(bid.content._id, {
        isSold: true,
        soldAt: new Date(),
        auctionStatus: "Ended",
        auctionEndedAt: new Date(),
      });
    }

    res.status(200).json({
      success: true,
      data: {
        order,
        payment,
        message:
          "Purchase completed successfully! You can now download your content.",
      },
    });
  } catch (err) {
    console.error("Error completing bid purchase:", err);
    next(err);
  }
};
