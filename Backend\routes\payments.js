const express = require('express');
const { check } = require('express-validator');
const {
  getPayments,
  getPayment,
  createPaymentIntent,
  confirmPayment,
  webhook,
  getBuyerPayments,
  getSellerPayments,
  processPayout,
  createConnectAccount,
  getConnectAccountStatus,
  createDashboardLink,
  updateConnectAccount,
  getConnectAccountDetails,
  createBidPaymentIntent,
  completeBidPurchase,
  calculateTax
} = require('../controllers/payments');

const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.post('/webhook', express.raw({ type: 'application/json' }), webhook);

// Protected routes
router.use(protect);

// Buyer routes
router.get('/buyer', authorize('buyer', 'admin'), getBuyerPayments);

router.post(
  '/create-intent',
  authorize('buyer', 'admin'),
  [
    check('orderId', 'Order ID is required').not().isEmpty()
  ],
  createPaymentIntent
);

router.post(
  '/confirm',
  authorize('buyer', 'admin'),
  [
    check('paymentIntentId', 'Payment intent ID is required').not().isEmpty(),
    check('orderId', 'Order ID is required').not().isEmpty()
  ],
  confirmPayment
);

router.post(
  '/calculate-tax',
  authorize('buyer', 'admin'),
  [
    check('orderId', 'Order ID is required').not().isEmpty(),
    check('amount', 'Amount is required').isNumeric(),
    check('customerDetails.address.line1', 'Street address is required').not().isEmpty(),
    check('customerDetails.address.city', 'City is required').not().isEmpty(),
    check('customerDetails.address.state', 'State is required').not().isEmpty(),
    check('customerDetails.address.postal_code', 'Postal code is required').not().isEmpty()
  ],
  calculateTax
);

// Test endpoint for tax calculation (development only)
if (process.env.NODE_ENV === 'development') {
  router.post('/test-tax', authorize('buyer', 'admin'), (req, res) => {
    res.json({
      success: true,
      message: 'Tax calculation endpoint is working',
      data: {
        taxCalculation: {
          id: 'test_123',
          amount_total: 10500, // $105.00
          tax_amount_exclusive: 500, // $5.00 tax
          tax_amount_inclusive: 0,
          line_items: [{
            amount: 10000, // $100.00
            amount_tax: 500, // $5.00
          }],
        }
      }
    });
  });
}

router.post(
  '/create-bid-payment-intent',
  authorize('buyer'),
  [
    check('bidId', 'Bid ID is required').not().isEmpty(),
    check('amount', 'Amount is required').isFloat({ min: 0.01 })
  ],
  createBidPaymentIntent
);

router.post(
  '/complete-bid-purchase',
  authorize('buyer'),
  [
    check('bidId', 'Bid ID is required').not().isEmpty(),
    check('paymentIntentId', 'Payment intent ID is required').not().isEmpty()
  ],
  completeBidPurchase
);

// Seller routes
router.post('/create-connect-account', authorize('seller'), createConnectAccount);
router.get('/connect-account-status/:accountId', authorize('seller'), getConnectAccountStatus);
router.get('/connect-account-details/:accountId', authorize('seller'), getConnectAccountDetails);
router.post('/update-connect-account', authorize('seller'), updateConnectAccount);
router.post('/create-dashboard-link', authorize('seller'), createDashboardLink);
router.get('/seller', authorize('seller'), getSellerPayments);

// Admin routes
router.get('/', authorize('admin'), getPayments);
router.post('/:id/payout', authorize('admin'), processPayout);

// Common routes
router.get('/:id', getPayment);

module.exports = router;
