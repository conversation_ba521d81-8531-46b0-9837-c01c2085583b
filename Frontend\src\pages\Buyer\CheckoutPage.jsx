import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";
import { getOrder } from "../../redux/slices/orderSlice";
import StripePaymentForm from "../../components/payment/StripePaymentForm";
import LoadingSkeleton from "../../components/common/LoadingSkeleton";
import { ErrorDisplay } from "../../components/common/ErrorBoundary";
import { toast } from "react-toastify";
import { VALIDATION, IMAGE_BASE_URL, GOOGLE_MAPS_CONFIG } from "../../utils/constants";
import { formatStandardDate } from "../../utils/dateValidation";
import "../../styles/CheckoutPage.css";

const CheckoutPage = () => {
  const { orderId } = useParams();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const autocompleteRef = useRef(null);
  const inputRef = useRef(null);

  const { user } = useSelector((state) => state.auth);
  const { isLoading, error } = useSelector((state) => state.order);

  const [paymentStep, setPaymentStep] = useState("loading"); // loading, payment, success, error
  const [currentOrder, setCurrentOrder] = useState(null); // Local state for current order
  const [address, setAddress] = useState(null); // Store selected address
  const [taxCalculation, setTaxCalculation] = useState(null); // Store Stripe tax calculation
  const [isAddressLoading, setIsAddressLoading] = useState(false);
  const [isGoogleMapsLoaded, setIsGoogleMapsLoaded] = useState(false);
  const [googleMapsError, setGoogleMapsError] = useState(null);

  // Load Google Maps Places API
  useEffect(() => {
    if (!GOOGLE_MAPS_CONFIG.API_KEY || GOOGLE_MAPS_CONFIG.API_KEY === 'your_google_maps_api_key_here') {
      setGoogleMapsError('Google Maps API key not configured. Please enter address manually.');
      console.warn('Google Maps API key not configured. Address autocomplete will not work.');
      return;
    }

    // Check if Google Maps is already loaded
    if (window.google && window.google.maps && window.google.maps.places) {
      initializeAutocomplete();
      return;
    }

    const script = document.createElement("script");
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_CONFIG.API_KEY}&libraries=${GOOGLE_MAPS_CONFIG.LIBRARIES.join(',')}&region=${GOOGLE_MAPS_CONFIG.REGION}`;
    script.async = true;
    script.onload = () => {
      setIsGoogleMapsLoaded(true);
      setGoogleMapsError(null);
      initializeAutocomplete();
    };
    script.onerror = () => {
      setGoogleMapsError('Failed to load Google Maps. Please enter address manually.');
      console.error('Failed to load Google Maps API');
    };

    document.head.appendChild(script);

    return () => {
      // Clean up script if component unmounts
      const existingScript = document.querySelector(`script[src*="maps.googleapis.com"]`);
      if (existingScript && existingScript.parentNode) {
        existingScript.parentNode.removeChild(existingScript);
      }
    };
  }, []);

  // Initialize Google Maps Autocomplete
  const initializeAutocomplete = () => {
    if (window.google && window.google.maps && window.google.maps.places && inputRef.current) {
      autocompleteRef.current = new window.google.maps.places.Autocomplete(
        inputRef.current,
        {
          types: ["address"],
          componentRestrictions: { country: "us" }, // Restrict to US addresses
          fields: ["address_components", "formatted_address", "geometry"],
        }
      );

      autocompleteRef.current.addListener("place_changed", () => {
        const place = autocompleteRef.current.getPlace();
        if (place.address_components) {
          const addressComponents = place.address_components;
          const formattedAddress = {
            street: "",
            city: "",
            state: "",
            postalCode: "",
            country: "US",
          };

          addressComponents.forEach((component) => {
            const types = component.types;
            if (types.includes("street_number")) {
              formattedAddress.street = component.long_name;
            }
            if (types.includes("route")) {
              formattedAddress.street += (formattedAddress.street ? " " : "") + component.long_name;
            }
            if (types.includes("locality")) {
              formattedAddress.city = component.long_name;
            }
            if (types.includes("administrative_area_level_1")) {
              formattedAddress.state = component.short_name;
            }
            if (types.includes("postal_code")) {
              formattedAddress.postalCode = component.long_name;
            }
            if (types.includes("country")) {
              formattedAddress.country = component.short_name;
            }
          });

          // Validate that we have the required fields
          if (formattedAddress.street && formattedAddress.city && formattedAddress.state && formattedAddress.postalCode) {
            setAddress(formattedAddress);
            calculateTax(formattedAddress);
          } else {
            toast.warning("Please select a complete address from the suggestions.");
          }
        }
      });
    }
  };

  // Fetch order details and check user permissions
  useEffect(() => {
    if (!user) {
      toast.error("Please log in to complete your purchase");
      navigate("/login");
      return;
    }

    const effectiveRole =
      user.role === "admin" ? user.role : user.activeRole || user.role;
    if (effectiveRole !== "buyer" && user.role !== "admin") {
      toast.error("Only buyers can make purchases");
      navigate("/");
      return;
    }

    if (!VALIDATION.isValidId(orderId)) {
      console.error("Invalid order ID:", orderId);
      toast.error("Invalid order ID. Please try creating a new order.");
      navigate("/buyer/dashboard");
      return;
    }

    dispatch(getOrder(orderId))
      .unwrap()
      .then((result) => {
        setCurrentOrder(result.data);
        setPaymentStep("payment");
      })
      .catch((err) => {
        console.error("Error fetching order:", err);
        toast.error("Order not found or you do not have permission to view it");
        navigate("/buyer/dashboard");
      });
  }, [dispatch, orderId, user, navigate]);

  // Calculate tax using Stripe Tax API
  const calculateTax = async (address) => {
    if (!address || !currentOrder) return;

    // Validate required address fields
    if (!address.street || !address.city || !address.state || !address.postalCode) {
      toast.error("Please select a complete address to calculate tax.");
      return;
    }

    setIsAddressLoading(true);
    try {
      // Get auth token from localStorage or Redux store
      const token = localStorage.getItem('token');

      // Get API base URL from constants or environment
      const apiBaseUrl = import.meta.env.VITE_API_BASE_URL || '/api';
      const taxEndpoint = `${apiBaseUrl}/payments/calculate-tax`;

      console.log("Calling tax calculation endpoint:", taxEndpoint);

      const response = await fetch(taxEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Authorization": `Bearer ${token}`
        },
        body: JSON.stringify({
          orderId: currentOrder._id,
          amount: currentOrder.amount * 100, // Convert to cents
          currency: "usd",
          customerDetails: {
            address: {
              line1: address.street,
              city: address.city,
              state: address.state,
              postal_code: address.postalCode,
              country: address.country || "US",
            },
          },
        }),
      });

      const taxData = await response.json();

      console.log("Tax calculation response:", {
        status: response.status,
        ok: response.ok,
        data: taxData
      });

      if (response.ok && taxData.success && taxData.data?.taxCalculation) {
        console.log("✅ Setting tax calculation:", taxData.data.taxCalculation);
        setTaxCalculation(taxData.data.taxCalculation);

        // Show different messages based on whether it's using the fallback or Stripe Tax
        if (taxData.data.taxCalculation.test_mode) {
          toast.success(`Tax calculated: $${(taxData.data.taxCalculation.tax_amount_exclusive / 100).toFixed(2)} for ${address.state}`);
        } else {
          toast.success("Tax calculated successfully!");
        }
      } else {
        console.error("Tax calculation error:", {
          response: response.status,
          data: taxData
        });

        // Create a simple fallback tax calculation on the frontend
        const state = address.state;
        const amount = currentOrder.amount * 100; // Convert to cents

        // Simple tax rate map (only used if backend fails completely)
        const stateTaxRates = {
          'CA': 0.0725, // California
          'NY': 0.08,   // New York
          'TX': 0.0625, // Texas
          'FL': 0.06,   // Florida
          // Add more states as needed
        };

        const taxRate = stateTaxRates[state] || 0.05; // Default 5% if state not found
        const taxAmount = Math.round(amount * taxRate);

        console.log(`Frontend fallback tax calculation for ${state}: ${taxRate * 100}%`);

        // Set a frontend fallback tax calculation
        setTaxCalculation({
          id: `frontend_fallback_${Date.now()}`,
          amount_total: taxAmount,
          tax_amount_exclusive: taxAmount,
          tax_amount_inclusive: 0,
          frontend_fallback: true
        });

        toast.warning("Using estimated tax. Actual tax may vary at checkout.");
      }
    } catch (error) {
      console.error("Error calculating tax:", error);
      toast.error("Failed to calculate tax. Please try again.");
    } finally {
      setIsAddressLoading(false);
    }
  };

  const handlePaymentSuccess = async (paymentResult) => {
    toast.success("Payment completed successfully!");
    setPaymentStep("success");

    try {
      const updatedOrderResult = await dispatch(
        getOrder(currentOrder._id)
      ).unwrap();
      const updatedOrder = updatedOrderResult.data;

      const getCardTypeDisplayName = (cardType) => {
        const cardTypeNames = {
          visa: "Visa",
          mastercard: "Mastercard",
          amex: "American Express",
          discover: "Discover",
          diners: "Diners Club",
          jcb: "JCB",
          unionpay: "UnionPay",
          unknown: "Card",
        };
        return cardTypeNames[cardType?.toLowerCase()] || "Card Payment";
      };

      const formatCardNumber = (lastFourDigits) => {
        if (lastFourDigits) {
          return `**** **** **** ${lastFourDigits}`;
        }
        return "**** **** **** ****";
      };

      const orderData = {
        orderId: `#${updatedOrder._id?.slice(-8) || "12345678"}`,
        date: formatStandardDate(updatedOrder.createdAt || Date.now()),
        time: new Date(updatedOrder.createdAt || Date.now()).toLocaleTimeString(
          "en-US",
          {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }
        ),
        items: 1,
        totalAmount: `$${(
          (updatedOrder.amount || 0) +
          (taxCalculation?.amount_total / 100 || 0)
        ).toFixed(2)}`,
        customerDetails: {
          name:
            updatedOrder.buyer?.firstName && updatedOrder.buyer?.lastName
              ? `${updatedOrder.buyer.firstName} ${updatedOrder.buyer.lastName}`
              : updatedOrder.buyer?.name || "Customer",
          email: updatedOrder.buyer?.email || "<EMAIL>",
          phone:
            updatedOrder.buyer?.mobile ||
            updatedOrder.buyer?.phone ||
            "Not provided",
          address: address || { street: "Not provided" },
        },
        paymentDetails: {
          method: getCardTypeDisplayName(updatedOrder.cardDetails?.cardType),
          cardNumber: formatCardNumber(
            updatedOrder.cardDetails?.lastFourDigits
          ),
          cardType: updatedOrder.cardDetails?.cardType || "unknown",
        },
        itemInfo: {
          title: updatedOrder.content?.title || "Digital Content",
          category:
            updatedOrder.content?.category ||
            updatedOrder.content?.sport ||
            "Sports Content",
          image:
            updatedOrder.content?.thumbnail ||
            updatedOrder.content?.thumbnailUrl ||
            "https://via.placeholder.com/80x80/f0f0f0/666666?text=Content",
        },
        fullOrder: updatedOrder,
        paymentResult: paymentResult,
        taxAmount: taxCalculation ? `$${(taxCalculation.amount_total / 100).toFixed(2)}` : "$0.00",
      };

      setTimeout(() => {
        navigate("/thank-you", {
          state: { orderData },
        });
      }, 2000);
    } catch (error) {
      console.error("Error fetching updated order:", error);
      const orderData = {
        orderId: `#${currentOrder._id?.slice(-8) || "12345678"}`,
        date: formatStandardDate(currentOrder.createdAt || Date.now()),
        time: new Date(currentOrder.createdAt || Date.now()).toLocaleTimeString(
          "en-US",
          {
            hour: "numeric",
            minute: "2-digit",
            hour12: true,
          }
        ),
        items: 1,
        totalAmount: `$${(
          (currentOrder.amount || 0) +
          (taxCalculation?.amount_total / 100 || 0)
        ).toFixed(2)}`,
        customerDetails: {
          name:
            currentOrder.buyer?.firstName && currentOrder.buyer?.lastName
              ? `${currentOrder.buyer.firstName} ${currentOrder.buyer.lastName}`
              : currentOrder.buyer?.name || "Customer",
          email: currentOrder.buyer?.email || "<EMAIL>",
          phone:
            currentOrder.buyer?.mobile ||
            currentOrder.buyer?.phone ||
            "Not provided",
          address: address || { street: "Not provided" },
        },
        paymentDetails: {
          method: "Card Payment",
          cardNumber: "**** **** **** ****",
          cardType: "unknown",
        },
        itemInfo: {
          title: currentOrder.content?.title || "Digital Content",
          category:
            currentOrder.content?.category ||
            currentOrder.content?.sport ||
            "Sports Content",
          image:
            currentOrder.content?.thumbnail ||
            currentOrder.content?.thumbnailUrl ||
            "https://via.placeholder.com/80x80/f0f0f0/666666?text=Content",
        },
        fullOrder: currentOrder,
        paymentResult: paymentResult,
        taxAmount: taxCalculation ? `$${(taxCalculation.amount_total / 100).toFixed(2)}` : "$0.00",
      };

      setTimeout(() => {
        navigate("/thank-you", {
          state: { orderData },
        });
      }, 200);
    }
  };

  const handlePaymentError = (error) => {
    console.error("Payment error:", error);
    toast.error(error.message || "Payment failed. Please try again.");
    setPaymentStep("error");
  };

  const handlePaymentCancel = () => {
    navigate(
      `/buyer/details/${currentOrder?.content?._id || currentOrder?.content}`
    );
  };

  const handleRetryPayment = () => {
    setPaymentStep("payment");
  };

  if (isLoading || paymentStep === "loading") {
    return <LoadingSkeleton type="checkout" />;
  }

  if (error || !currentOrder) {
    return (
      <ErrorDisplay
        title="Order Not Found"
        message={
          error ||
          "The order you're looking for doesn't exist or you don't have permission to view it."
        }
        onRetry={() => navigate("/buyer/dashboard")}
        retryText="Go to Dashboard"
      />
    );
  }

  if (currentOrder.buyer._id !== user._id && currentOrder.buyer !== user._id) {
    return (
      <ErrorDisplay
        title="Access Denied"
        message="You don't have permission to view this order."
        onRetry={() => navigate("/buyer/dashboard")}
        retryText="Go to Dashboard"
      />
    );
  }

  if (currentOrder.paymentStatus === "Completed") {
    return (
      <div className="checkout-page">
        <div className="max-container">
          <div>
            <div className="order-already-paid">
              <h2>Order Already Paid</h2>
              <p>This order has already been completed.</p>
              <button
                className="btn-primary"
                onClick={() => navigate("/buyer/downloads")}
              >
                View Downloads
              </button>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="checkout-page">
      <div className="max-container">
        <div className="checkout-content">
          {/* Left Section - Payment Form */}
          <div className="checkout-left">
            <div className="checkout-form-container">
              <h1 className="checkout-title">Complete Your Purchase</h1>

              {/* Address Input Field */}
              <div className="address-section">
                <h3>Billing Address</h3>
                <div className="address-input-container">
                  <input
                    ref={inputRef}
                    type="text"
                    placeholder={googleMapsError ? "Enter address manually" : "Start typing your address for suggestions"}
                    className={`address-input ${googleMapsError ? 'error' : ''}`}
                    disabled={isAddressLoading}
                  />
                  {isAddressLoading && (
                    <div className="address-loading">
                      <div className="spinner"></div>
                      <p>Calculating tax...</p>
                    </div>
                  )}
                </div>

                {/* Test Addresses for Development */}
                {import.meta.env.DEV && (
                  <div className="test-addresses" style={{ marginTop: '10px', display: 'flex', gap: '10px', flexWrap: 'wrap' }}>
                    <button
                      type="button"
                      onClick={() => {
                        const nyAddress = {
                          street: "123 Broadway",
                          city: "New York",
                          state: "NY",
                          postalCode: "10001",
                          country: "US"
                        };
                        setAddress(nyAddress);
                        calculateTax(nyAddress);
                        if (inputRef.current) {
                          inputRef.current.value = "123 Broadway, New York, NY 10001";
                        }
                      }}
                      style={{ padding: '5px 10px', fontSize: '12px', backgroundColor: '#e3f2fd', border: '1px solid #2196f3', borderRadius: '4px', cursor: 'pointer' }}
                    >
                      Test NY (8%)
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const txAddress = {
                          street: "456 Main St",
                          city: "Austin",
                          state: "TX",
                          postalCode: "73301",
                          country: "US"
                        };
                        setAddress(txAddress);
                        calculateTax(txAddress);
                        if (inputRef.current) {
                          inputRef.current.value = "456 Main St, Austin, TX 73301";
                        }
                      }}
                      style={{ padding: '5px 10px', fontSize: '12px', backgroundColor: '#fff3e0', border: '1px solid #ff9800', borderRadius: '4px', cursor: 'pointer' }}
                    >
                      Test TX (6.25%)
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        const orAddress = {
                          street: "789 Oak Ave",
                          city: "Portland",
                          state: "OR",
                          postalCode: "97201",
                          country: "US"
                        };
                        setAddress(orAddress);
                        calculateTax(orAddress);
                        if (inputRef.current) {
                          inputRef.current.value = "789 Oak Ave, Portland, OR 97201";
                        }
                      }}
                      style={{ padding: '5px 10px', fontSize: '12px', backgroundColor: '#e8f5e8', border: '1px solid #4caf50', borderRadius: '4px', cursor: 'pointer' }}
                    >
                      Test OR (0%)
                    </button>
                  </div>
                )}

                {googleMapsError && (
                  <p className="address-error">
                    {googleMapsError}
                  </p>
                )}

                {address && (
                  <div className="selected-address-container">
                    <p className="selected-address">
                      <strong>Selected Address:</strong> {address.street}, {address.city}, {address.state}{" "}
                      {address.postalCode}
                    </p>
                    <button
                      className="change-address-btn"
                      onClick={() => {
                        setAddress(null);
                        setTaxCalculation(null);
                        if (inputRef.current) {
                          inputRef.current.value = '';
                          inputRef.current.focus();
                        }
                      }}
                    >
                      Change
                    </button>
                  </div>
                )}
              </div>

              {paymentStep === "loading" && (
                <div className="payment-loading">
                  <p>Loading order details...</p>
                </div>
              )}

              {paymentStep === "payment" && currentOrder ? (
                <>
                  <StripePaymentForm
                    order={currentOrder}
                    taxCalculation={taxCalculation}
                    onSuccess={handlePaymentSuccess}
                    onError={handlePaymentError}
                    onCancel={handlePaymentCancel}
                  />
                </>
              ) : (
                <div className="debug-info"></div>
              )}

              {paymentStep === "success" && (
                <div className="payment-success">
                  <div className="success-icon">✅</div>
                  <h3>Payment Successful!</h3>
                  <p>
                    Your payment has been processed successfully. Redirecting...
                  </p>
                </div>
              )}

              {paymentStep === "error" && (
                <div className="payment-error">
                  <div className="error-icon">❌</div>
                  <h3>Payment Failed</h3>
                  <p>
                    There was an issue processing your payment. Please try
                    again.
                  </p>
                  <button
                    className="btn-primary retry-btn"
                    onClick={handleRetryPayment}
                  >
                    Retry Payment
                  </button>
                </div>
              )}
            </div>
          </div>

          {/* Right Section - Order Summary */}
          <div className="checkout-right">
            <div className="order-summary">
              <h2 className="order-title">Order Summary</h2>

              <div className="rightbackgrounddiv">
                {/* Item Info */}
                <div className="item-info-section">
                  <h3 className="item-info-title">Item Details</h3>

                  <div className="item-details">
                    <div className="item-image">
                      <img
                        src={
                          currentOrder.content?.thumbnailUrl
                            ? `${IMAGE_BASE_URL}${currentOrder.content.thumbnailUrl}`
                            : "https://via.placeholder.com/80x80/f5f5f5/666666?text=IMG"
                        }
                        alt={currentOrder.content?.title || "Content"}
                        className="item-thumbnail"
                      />
                    </div>

                    <div className="item-description">
                      <h4 className="item-name">
                        {currentOrder.content?.title || "Content Title"}
                      </h4>
                      <p className="item-coach">
                        By {currentOrder.content?.coachName || "Coach"}
                      </p>
                      <p className="item-type">
                        {currentOrder.content?.contentType || "Digital Content"}
                      </p>
                    </div>
                  </div>
                </div>

                {/* Order Info */}
                <div className="order-info-section">
                  <h3 className="order-info-title">Order Information</h3>
                  <div className="order-details">
                    <div className="order-row">
                      <span>Order ID:</span>
                      <span>#{currentOrder._id?.slice(-8).toUpperCase()}</span>
                    </div>
                    <div className="order-row">
                      <span>Order Type:</span>
                      <span>{currentOrder.orderType}</span>
                    </div>
                    <div className="order-row">
                      <span>Status:</span>
                      <span
                        className={`status ${currentOrder.status?.toLowerCase()}`}
                      >
                        {currentOrder.status}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Pricing */}
                <div className="pricing-section">
                  <div className="price-row">
                    <span className="price-label">Subtotal</span>
                    <span className="price-value">
                      ${(currentOrder.amount || 0).toFixed(2)}
                    </span>
                  </div>

                  <div className="price-row">
                    <span className="price-label">Tax</span>
                    <span className="price-value">
                      {isAddressLoading
                        ? "Calculating..."
                        : taxCalculation
                          ? `$${(taxCalculation.amount_total / 100).toFixed(2)}`
                          : address ? "$0.00" : "Enter address"}
                    </span>
                  </div>



                  <div className="price-row total-row">
                    <span className="price-label">Total</span>
                    <span className="price-value">
                      {isAddressLoading
                        ? "Calculating..."
                        : `$${(
                            (currentOrder.amount || 0) +
                            (taxCalculation?.amount_total / 100 || 0)
                          ).toFixed(2)}`}
                    </span>
                  </div>


                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutPage;