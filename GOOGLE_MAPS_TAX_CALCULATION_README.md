# Google Maps Address Autocomplete & Dynamic Tax Calculation

This document explains the implementation of Google Maps address autocomplete and dynamic tax calculation functionality in the XO Sports Hub checkout system.

## 🏗️ Architecture Overview

The system consists of two main components:
1. **Google Maps Places API Integration** - For address autocomplete
2. **Dynamic Tax Calculation** - Using Stripe Tax API (production) or hardcoded rates (testing)

## 🔧 Local Development Setup

### Prerequisites
- Node.js (v16 or higher)
- MongoDB running locally
- Google Maps API key
- Stripe account (test mode)

### Environment Configuration

#### Frontend (.env)
```bash
# API Configuration
VITE_API_BASE_URL=http://localhost:5000/api
VITE_IMAGE_BASE_URL=http://localhost:5000

# Google Maps Configuration
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Stripe Configuration
VITE_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key_here
```

#### Backend (.env)
```bash
# Server Configuration
PORT=5000
NODE_ENV=development

# Database
MONGODB_URI=mongodb://localhost:27017/xosportshub

# Stripe Configuration
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key_here
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret_here
```

### Google Maps API Setup

1. **Create Google Cloud Project**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one

2. **Enable Required APIs**
   ```bash
   - Maps JavaScript API
   - Places API
   ```

3. **Create API Key**
   - Go to "Credentials" → "Create Credentials" → "API Key"
   - Restrict the key to your domain for security
   - Add to `VITE_GOOGLE_MAPS_API_KEY` in frontend .env

4. **API Key Restrictions (Recommended)**
   ```
   Application restrictions: HTTP referrers
   Website restrictions: 
   - http://localhost:5173/*
   - https://yourdomain.com/*
   
   API restrictions:
   - Maps JavaScript API
   - Places API
   ```

## 🧪 Local Test Mode

### How It Works

In development mode (`NODE_ENV=development`), the system uses **hardcoded tax rates** instead of Stripe Tax API for reliable testing.

#### Hardcoded Tax Rates
```javascript
const testTaxRates = {
  'NY': 0.08,   // New York - 8%
  'TX': 0.0625, // Texas - 6.25%
  'CA': 0.0725, // California - 7.25%
  'FL': 0.06,   // Florida - 6%
  'WA': 0.065,  // Washington - 6.5%
  'OR': 0.0,    // Oregon - 0% (no sales tax)
  'NH': 0.0,    // New Hampshire - 0%
  'MT': 0.0,    // Montana - 0%
  'DE': 0.0,    // Delaware - 0%
  'AK': 0.0,    // Alaska - 0%
};
```

#### Test Features Available

1. **Quick Test Buttons** (Development Only)
   - Test NY (8%) - Simulates New York address
   - Test TX (6.25%) - Simulates Texas address
   - Test OR (0%) - Simulates Oregon address

2. **Address Autocomplete Testing**
   - Type any US address
   - Select from Google Places suggestions
   - Tax calculated based on state

3. **API Endpoint Testing**
   ```bash
   POST /api/payments/calculate-tax
   ```

### Testing Workflow

1. **Start Development Servers**
   ```bash
   # Backend
   cd Backend
   npm run dev

   # Frontend
   cd Frontend
   npm run dev
   ```

2. **Navigate to Checkout Page**
   ```
   http://localhost:5173/checkout/{orderId}
   ```

3. **Test Address Autocomplete**
   - Click test buttons OR
   - Type address manually
   - Verify tax calculation appears

4. **Expected Results**
   ```
   Item: $100.00
   Tax (NY): $8.00
   Total: $108.00
   ```

### Debug Information

The system provides extensive logging in development:

```javascript
// Console logs you'll see:
✅ Card element mounted successfully
✅ Setting tax calculation: {amount_total: 10800, tax_amount_exclusive: 800}
Tax calculated: $8.00 for NY
```

## 🚀 Production Setup

### Environment Configuration

#### Frontend (.env.production)
```bash
VITE_API_BASE_URL=https://api.yourdomain.com/api
VITE_IMAGE_BASE_URL=https://api.yourdomain.com
VITE_GOOGLE_MAPS_API_KEY=your_production_google_maps_api_key
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_live_stripe_key
```

#### Backend (.env.production)
```bash
NODE_ENV=production
PORT=5000
MONGODB_URI=mongodb://your-production-db-url
STRIPE_SECRET_KEY=sk_live_your_live_stripe_key
STRIPE_WEBHOOK_SECRET=whsec_your_production_webhook_secret
```

### Production Tax Calculation

In production, the system uses **Stripe Tax API** for accurate, real-time tax calculation.

#### Stripe Tax Setup

1. **Enable Stripe Tax**
   - Log into Stripe Dashboard
   - Go to "Products" → "Tax"
   - Enable Stripe Tax for your account

2. **Configure Tax Settings**
   ```
   - Business location
   - Tax registration numbers
   - Product tax codes
   ```

3. **Update Backend Code**
   ```javascript
   // Replace hardcoded rates with Stripe Tax API
   const taxCalculation = await stripe.tax.calculations.create({
     currency: 'usd',
     line_items: lineItems,
     customer_details: {
       address: customerAddress,
       address_source: 'billing',
     },
     expand: ['line_items.data.tax_breakdown'],
   });
   ```

### Production Features

1. **Real-time Tax Calculation**
   - Accurate rates for all US states/localities
   - Automatic updates when tax laws change
   - Compliance with tax regulations

2. **Enhanced Security**
   - API key restrictions
   - HTTPS enforcement
   - Rate limiting

3. **Error Handling**
   - Fallback to estimated rates if Stripe Tax fails
   - Graceful degradation
   - User-friendly error messages

## 🔄 Switching Between Modes

### Development → Production

1. **Update Environment Variables**
   ```bash
   NODE_ENV=production
   STRIPE_SECRET_KEY=sk_live_...
   VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...
   ```

2. **Enable Stripe Tax API**
   ```javascript
   // In Backend/controllers/payments.js
   // Comment out hardcoded rates section
   // Uncomment Stripe Tax API section
   ```

3. **Remove Test Features**
   - Test buttons automatically hidden in production
   - Debug logs disabled

### Production → Development

1. **Update Environment Variables**
   ```bash
   NODE_ENV=development
   STRIPE_SECRET_KEY=sk_test_...
   VITE_STRIPE_PUBLISHABLE_KEY=pk_test_...
   ```

2. **Enable Hardcoded Rates**
   - Automatic fallback to test rates
   - Test buttons appear

## 📊 API Endpoints

### Tax Calculation
```http
POST /api/payments/calculate-tax
Authorization: Bearer {jwt_token}
Content-Type: application/json

{
  "orderId": "507f1f77bcf86cd799439011",
  "amount": 10000,
  "currency": "usd",
  "customerDetails": {
    "address": {
      "line1": "123 Main Street",
      "city": "New York",
      "state": "NY",
      "postal_code": "10001",
      "country": "US"
    }
  }
}
```

### Response
```json
{
  "success": true,
  "data": {
    "taxCalculation": {
      "id": "test_tax_1234567890",
      "amount_total": 10800,
      "tax_amount_exclusive": 800,
      "tax_amount_inclusive": 0,
      "test_mode": true
    }
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Google Maps Not Loading**
   ```
   Check: API key configuration
   Check: API restrictions
   Check: Billing enabled in Google Cloud
   ```

2. **Tax Calculation Stuck on "Calculating..."**
   ```
   Check: Backend server running
   Check: JWT token valid
   Check: Network requests in browser dev tools
   ```

3. **Stripe Tax Errors**
   ```
   Check: Stripe Tax enabled
   Check: Business location configured
   Check: API keys correct
   ```

### Debug Commands

```bash
# Test tax calculation directly
curl -X POST http://localhost:5000/api/payments/calculate-tax \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"orderId":"test","amount":10000,"customerDetails":{"address":{"line1":"123 Main St","city":"New York","state":"NY","postal_code":"10001","country":"US"}}}'

# Check Google Maps API
curl "https://maps.googleapis.com/maps/api/js?key=YOUR_API_KEY&libraries=places"
```

## 📈 Monitoring & Analytics

### Production Monitoring

1. **Tax Calculation Success Rate**
2. **Google Maps API Usage**
3. **Error Rates and Types**
4. **Performance Metrics**

### Recommended Tools

- **Stripe Dashboard** - Tax calculation monitoring
- **Google Cloud Console** - Maps API usage
- **Application Logs** - Error tracking
- **Performance Monitoring** - Response times

## 🔒 Security Considerations

1. **API Key Security**
   - Restrict Google Maps API key to specific domains
   - Use environment variables, never hardcode
   - Rotate keys regularly

2. **Stripe Security**
   - Use webhook signatures
   - Validate all requests server-side
   - Never expose secret keys to frontend

3. **Data Protection**
   - Encrypt sensitive customer data
   - Comply with PCI DSS requirements
   - Implement proper access controls

## 📝 Maintenance

### Regular Tasks

1. **Update Tax Rates** (if using hardcoded rates)
2. **Monitor API Usage** and costs
3. **Update Dependencies** regularly
4. **Test Functionality** after updates
5. **Review Error Logs** weekly

### Version Updates

- **Google Maps API**: Check for new features/deprecations
- **Stripe API**: Update to latest version for new features
- **Dependencies**: Keep all packages updated for security

---

## 🚀 Quick Start Guide

### 1. Clone and Setup
```bash
git clone <repository-url>
cd xosportshub

# Install dependencies
cd Backend && npm install
cd ../Frontend && npm install
```

### 2. Configure Environment
```bash
# Copy example files
cp Frontend/.env.example Frontend/.env
cp Backend/.env.example Backend/.env

# Add your API keys to the .env files
```

### 3. Get API Keys
- **Google Maps**: [Google Cloud Console](https://console.cloud.google.com/)
- **Stripe**: [Stripe Dashboard](https://dashboard.stripe.com/)

### 4. Start Development
```bash
# Terminal 1 - Backend
cd Backend && npm run dev

# Terminal 2 - Frontend
cd Frontend && npm run dev
```

### 5. Test the Feature
1. Navigate to `http://localhost:5173/checkout/{orderId}`
2. Click "Test NY (8%)" button
3. Verify tax calculation works

## 📋 Feature Checklist

### Development Mode ✅
- [x] Hardcoded tax rates for testing
- [x] Quick test buttons for different states
- [x] Google Maps address autocomplete
- [x] Real-time tax calculation
- [x] Debug logging and error handling
- [x] Fallback mechanisms

### Production Mode 🚀
- [ ] Stripe Tax API integration
- [ ] Production Google Maps API key
- [ ] HTTPS enforcement
- [ ] Error monitoring
- [ ] Performance optimization
- [ ] Security hardening

## 🔄 Zero-Code Migration: Test → Production

The system is designed to automatically switch between test and production modes based on environment variables, requiring **zero code changes** when deploying to production.

### Environment-Based Configuration

```javascript
// This code is already implemented in Backend/controllers/payments.js
// No changes needed when deploying to production!

// Environment-based tax calculation
const useStripeTax = process.env.NODE_ENV === 'production' && process.env.ENABLE_STRIPE_TAX === 'true';

if (useStripeTax) {
  // Production: Use Stripe Tax API
  taxCalculation = await stripe.tax.calculations.create({...});
} else {
  // Development/Test: Use hardcoded tax rates
  // Also serves as fallback if Stripe Tax fails
  taxCalculation = createTestTaxCalculation(state, amount);
}
```

### Step 1: Simply Update Environment Variables
```bash
# Production environment
NODE_ENV=production
ENABLE_STRIPE_TAX=true  # Set to 'false' to force test rates in production
STRIPE_SECRET_KEY=sk_live_...
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_...
VITE_GOOGLE_MAPS_API_KEY=production_key_here
```

### Step 2: Enable Stripe Tax (One-time setup)
1. Login to Stripe Dashboard
2. Navigate to Products → Tax
3. Complete business setup
4. Enable tax calculation

### Step 3: Deploy (Zero Code Changes!)
```bash
# Build for production
npm run build

# Deploy to your hosting platform with production environment variables
# The system automatically switches to Stripe Tax API!
```

### Deployment Modes

#### Development Mode
```bash
NODE_ENV=development
ENABLE_STRIPE_TAX=false
# Uses hardcoded test rates + test buttons
```

#### Production Mode (Stripe Tax)
```bash
NODE_ENV=production
ENABLE_STRIPE_TAX=true
# Uses Stripe Tax API with fallback to test rates
```

#### Production Mode (Test Rates)
```bash
NODE_ENV=production
ENABLE_STRIPE_TAX=false
# Uses hardcoded test rates (no test buttons)
```

### Automatic Fallback System

The system includes intelligent fallback:
1. **Primary**: Stripe Tax API (if enabled and working)
2. **Fallback**: Hardcoded test rates (if Stripe Tax fails)
3. **Graceful**: User sees tax calculation regardless of backend issues

## 📊 Cost Estimation

### Google Maps API Costs
```
Places API (Autocomplete): $2.83 per 1,000 requests
Maps JavaScript API: $2.00 per 1,000 loads

Estimated monthly cost for 10,000 checkouts: ~$50
```

### Stripe Tax Costs
```
Tax calculation: $0.50 per 1,000 calculations
Included in Stripe processing fees for most plans

Estimated monthly cost for 10,000 checkouts: ~$5
```

## 🎯 Performance Optimization

### Frontend Optimizations
```javascript
// Lazy load Google Maps
const loadGoogleMaps = () => {
  return new Promise((resolve) => {
    if (window.google) {
      resolve(window.google);
      return;
    }
    // Load script dynamically
  });
};

// Debounce tax calculations
const debouncedTaxCalculation = debounce(calculateTax, 500);
```

### Backend Optimizations
```javascript
// Cache tax calculations
const taxCache = new Map();
const cacheKey = `${state}-${amount}`;

if (taxCache.has(cacheKey)) {
  return taxCache.get(cacheKey);
}
```

## 🧪 Testing Scenarios

### Manual Testing Checklist
- [ ] Address autocomplete works
- [ ] Tax calculation for NY (8%)
- [ ] Tax calculation for TX (6.25%)
- [ ] Tax calculation for OR (0%)
- [ ] Error handling for invalid addresses
- [ ] Mobile responsiveness
- [ ] Payment flow completion

### Automated Testing
```javascript
// Example test
describe('Tax Calculation', () => {
  test('calculates NY tax correctly', async () => {
    const result = await calculateTax({
      state: 'NY',
      amount: 10000
    });
    expect(result.tax_amount_exclusive).toBe(800);
  });
});
```

For additional support or questions, please refer to the main project documentation or contact the development team.
